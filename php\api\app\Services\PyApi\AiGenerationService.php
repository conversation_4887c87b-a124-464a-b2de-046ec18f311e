<?php

namespace App\Services\PyApi;

use App\Jobs\ProcessTextGeneration;
use App\Helpers\LogCheckHelper;
use App\Enums\ApiCodeEnum;
use App\Models\AiModelConfig;
use App\Models\AiGenerationTask;
use App\Models\StyleLibrary;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

/**
 * AI生成服务
 */
class AiGenerationService
{
    protected $aiModelService;
    protected $pointsService;

    public function __construct(AiModelService $aiModelService, PointsService $pointsService)
    {
        $this->aiModelService = $aiModelService;
        $this->pointsService = $pointsService;
    }

    // 创建异步任务（在这里执行异步任务前冻结积分）
    public function generateTextWithWebSocket($request, $userId)
    {
        try {
            // 验证WebSocket会话ID
            $webSocketSessionId = $request->get('websocket_session_id');
            $webSocketService = app(WebSocketService::class);
            $sessionValid = $webSocketService->validateSession($webSocketSessionId, $userId);
            if (!$sessionValid) 
            {
                return [
                    'code' => ApiCodeEnum::VALIDATION_ERROR,
                    'message' => 'WebSocket会话无效',
                    'data' => []
                ];
            }

            // 验证项目ID和项目拥有者
            $projectId = $request->get('project_id');
            if ($projectId) {
                $project = \App\Models\Project::find($projectId);
                if (!$project) 
                {
                    return [
                        'code' => ApiCodeEnum::NOT_FOUND,
                        'message' => '指定的项目不存在',
                        'data' => []
                    ];
                }

                // 验证用户是否为项目的拥有者
                if ($project->user_id !== $userId) 
                {
                    return [
                        'code' => ApiCodeEnum::FORBIDDEN,
                        'message' => '您没有权限访问该项目',
                        'data' => []
                    ];
                }
            }

            // 🎯 积分预检查需要的生成参数
            $generationParams = [
                'max_tokens' => $request->get('max_tokens', 1000),
                'temperature' => $request->get('temperature', 0.7),
                'top_p' => $request->get('top_p', 0.9)
            ];

            // 🎯 调用服务层进行积分预检查
            $pointsCheckResult = $this->checkPointsForWebSocketTextGeneration(
                $userId,
                $request->get('prompt'),
                $request->get('model_id'),
                $generationParams
            );

            // 积分检查失败，直接返回错误，不创建异步任务
            if ($pointsCheckResult['code'] !== ApiCodeEnum::SUCCESS) 
            {
                return [
                    'code' => $pointsCheckResult['code'],
                    'message' => $pointsCheckResult['message'],
                    'data' => $pointsCheckResult['data'] ?? []
                ];
            }

            // 所需积分
            $estimatedCost = $pointsCheckResult['data']['estimated_cost'];

            // 冻结积分
            $freezeResult = $this->pointsService->freezePoints(
                $userId,
                $estimatedCost,
                'text_generation',
                null,
                300 // 5分钟超时
            );

            // 积分冻结失败时，回滚事务并返回错误，阻止任务创建
            if ($freezeResult['code'] !== ApiCodeEnum::SUCCESS) {
                Log::warning('积分冻结失败，阻止任务创建', [
                    'user_id' => $userId,
                    'estimated_cost' => $estimatedCost,
                    'freeze_result' => $freezeResult
                ]);
                return $freezeResult;
            }

            // 积分充足，创建异步任务
            $taskId = 'text_gen_' . time() . '_' . Str::random(8);
            $prompt = $request->get('prompt');
            $modelId = $request->get('model_id');

            // 上下文（设置AI扮演角色的作用）
            $context = $request->get('context', 'prompt_edit');

            // 预算积分
            $generationParams['estimated_cost'] = $estimatedCost;

            // 创建异步任务(ProcessTextGeneration)->AiGenerationService.generateText
            dispatch(new ProcessTextGeneration(
                $taskId,
                $userId,
                $prompt,
                $modelId,
                $request->get('project_id'),
                $generationParams,
                $context
            ));

            // 返回任务信息
            $responseData = [
                'task_id' => $taskId,
                'status' => 'processing',
                'context' => $context,
                'estimated_cost' => $estimatedCost,
                'prompt' => substr($prompt, 0, 100),
                'generation_params' => $generationParams,
                'websocket_session_id' => $webSocketSessionId,
                'timestamp' => \Carbon\Carbon::now()->format('c'),
                'request_id' => 'req_' . Str::random(16)
            ];

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '文本生成任务创建成功',
                'data' => $responseData
            ];
        } 
        catch (\Exception $e)
        {
            $error_context = [
                'user_id' => $userId,
                'prompt' => substr($request->get('prompt'), 0, 100),
                'model_id' => $request->get('model_id'),
                'project_id' => $request->get('project_id')
            ];

            Log::error('文本生成任务创建失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '文本生成任务创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 生成文本
     *
     * 🚨 架构边界规范：
     * ✅ 工具API接口服务负责调用AI服务，不进行模拟
     * ✅ 使用 AiServiceClient 实现环境切换
     * ❌ 不在工具API中进行模拟返回数据
     *
     * 🚨 业务流程：
     * 1. 解析提示词中的项目参数（比例、风格）
     * 2. 根据参数创建或关联项目
     * 3. 获取或创建AI模型配置
     * 4. 预扣积分并创建生成任务
     * 5. 此服务只支持WebSocket异步执行文本生成
     *
     * @param int $userId 用户ID
     * @param string $prompt 生成提示词（可能包含JSON格式的项目参数）
     * @param int|null $modelId AI模型配置ID，为空时使用默认模型
     * @param int|null $projectId 项目ID，为空时根据提示词参数创建项目
     * @param array $generationParams 生成参数（temperature、max_tokens等）
     * @param string|null $businessType 业务类型（php\api\config\ai.php business_type_mappings 中的后端任务类型，映射后的类型）
     *
     * @return array 返回结果数组
     *   - code: 状态码（ApiCodeEnum）
     *   - message: 提示信息
     *   - data: 任务数据
     *     - task_id: 任务ID
     *     - status: 任务状态
     *     - estimated_time: 预估完成时间
     *     - project_id: 项目ID（如果有）
     *
     * @throws \Exception 当业务逻辑异常时抛出 
     */
    public function generateText(int $userId, string $prompt, ?int $modelId = null, ?int $projectId = null, array $generationParams = [], ?string $businessType = null, ?string $externalTaskId = null): array
    {
        try {
            DB::beginTransaction();

            // 确定任务类型：优先使用传递的businessType，否则使用默认值
            $taskType = $businessType ?: AiGenerationTask::TYPE_TEXT_GENERATION;

            // 从$prompt中提取比例
            $promptData = json_decode($prompt, true);

            // 从$prompt中提取比例
            $aspectRatio = $promptData['比例'] ?? '';

            // 从$prompt中提取风格
            $styleId = $promptData['风格'] ?? '';

            // 'text' => 'text_generation',                    // 文生文返回无格式要求文本
            // 'prompt' => 'text_generation_prompt',           // 单独对生图提示词进行扩写
            // 'story' => 'text_generation_story',             // 单独对故事进行扩写
            // 'storyboard' => 'text_generation_storyboard',   // 根据故事进行分镜：一次满足多条件
            // if($taskType == 'text')
            // {
            // }

            // 从$prompt中提取提示词
            $prompt = $promptData['提示词'] ?? '';

            // 获取模型配置
            if ($modelId) 
            {
                $model = AiModelConfig::active()->find($modelId);
            } 
            else 
            {
                $model = $this->aiModelService->getUserDefaultModel($userId, AiModelConfig::TYPE_TEXT_GENERATION);
            }

            if (!$model) 
            {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有可用的文本生成模型',
                    'data' => []
                ];
            }

            // 检查模型健康状态
            if (!$model->isHealthy()) {
                return [
                    'code' => ApiCodeEnum::SERVICE_UNAVAILABLE,
                    'message' => '模型服务当前不可用',
                    'data' => []
                ];
            }

            // 创建生成任务
            $task = AiGenerationTask::create([
                'user_id' => $userId,
                'project_id' => $projectId,
                'model_config_id' => $model->id,
                'task_type' => $taskType,
                'platform' => $model->platform,
                'model_name' => $model->model_name,
                'status' => AiGenerationTask::STATUS_PENDING,
                'input_data' => [
                    'prompt' => $prompt,
                    'max_tokens' => $generationParams['max_tokens'] ?? 1000,
                    'temperature' => $generationParams['temperature'] ?? 0.7,
                    'top_p' => $generationParams['top_p'] ?? 0.9
                ],
                'generation_params' => $generationParams,
                'external_task_id' => $externalTaskId,
                'cost' => $generationParams['estimated_cost']
            ]);

            // 异步执行生成任务
            $aiTextData = $this->executeTextGeneration($task);

            // 项目更新、分镜入库、生图提示词修改
            if($aiTextData['code'] === ApiCodeEnum::SUCCESS)
            {
                // 导入分镜服务层
                $ProjectStoryboardService = new \App\Services\PyApi\ProjectStoryboardService();

                // 根据以下类型对项目或分镜进行更新
                // 1、更新项目
                if($taskType == 'text_generation_storyboard')
                {
                    // 数据验证是否符合更新项目需求
                    if($aspectRatio || $styleId)
                    {
                        if($aspectRatio && $styleId)
                        {
                            // 更新项目故事标题、故事简概、风格、规格
                            $result = $ProjectStoryboardService->extractStoryboardsFromStory($userId, $projectId, $styleId, $aspectRatio, $aiTextData['data']['result']['text']);
                        }
                    }
                }

                // 2、更新项目故事
                if($taskType == 'text_generation')
                {
                    // 1、故事扩写内容更新到 p_projects.story_content
                }

                // 3、更新生图提示词
                if($taskType == 'text_generation_prompt')
                {
                    // 1、提示词扩写内容更新到 p_storyboards.ai_prompt
                }
            }

            // // 返回数据
            // $responseData = [
            //     'task_id' => $task->id,
            //     'status' => 'completed',
            //     'result' => [
            //         'text' => $generatedText,
            //         'finish_reason' => $outputData['finish_reason'],
            //         'model' => $outputData['model'],
            //         'usage' => $outputData['usage'],
            //         'tokens_used' => $tokensUsed
            //     ],
            //     'project_id' => $task->project_id,
            //     'context' => $task->context,
            //     'processing_time_ms' => $task->processing_time_ms,
            //     'timestamp' => Carbon::now()->format('c')
            // ];

            // return [
            //     'code' => ApiCodeEnum::SUCCESS,
            //     'message' => '文本生成成功',
            //     'data' => $responseData
            // ];

            Log::info('文本生成任务创建成功', [
                'task_id' => $task->id,
                'user_id' => $userId,
                'model_id' => $model->id,
                'estimated_cost' => $generationParams['estimated_cost']
            ]);

            DB::commit();

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '文本生成任务已创建',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'estimated_cost' => $generationParams['estimated_cost'],
                    'model_name' => $model->model_name,
                    'platform' => $model->platform
                ]
            ];

        } 
        catch (\Exception $e) 
        {
            DB::rollBack();

            $error_context = [
                'user_id' => $userId,
                'prompt' => substr($prompt, 0, 100),
                'model_id' => $modelId,
                'project_id' => $projectId,
                'generation_params_count' => is_array($generationParams) ? count($generationParams) : 0,
            ];

            Log::error('文本生成任务创建失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '文本生成任务创建失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取任务状态
     */
    public function getTaskStatus(int $taskId, int $userId): array
    {
        try {
            $task = AiGenerationTask::byUser($userId)->find($taskId);
            
            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'id' => $task->id,
                    'task_type' => $task->task_type,
                    'status' => $task->status,
                    'platform' => $task->platform,
                    'model_name' => $task->model_name,
                    'input_data' => $task->input_data,
                    'output_data' => $task->output_data,
                    'cost' => $task->cost,
                    'tokens_used' => $task->tokens_used,
                    'processing_time_ms' => $task->processing_time_ms,
                    'error_message' => $task->error_message,
                    'retry_count' => $task->retry_count,
                    'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                    'started_at' => $task->started_at?->format('Y-m-d H:i:s'),
                    'completed_at' => $task->completed_at?->format('Y-m-d H:i:s')
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('获取任务状态失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取任务状态失败',
                'data' => null
            ];
        }
    }

    /**
     * 获取用户任务列表
     */
    public function getUserTasks(int $userId, array $filters, int $page, int $perPage): array
    {
        try {
            $query = AiGenerationTask::byUser($userId);
            
            // 应用筛选条件
            if (!empty($filters['task_type'])) {
                $query->byType($filters['task_type']);
            }
            
            if (!empty($filters['status'])) {
                $query->byStatus($filters['status']);
            }
            
            if (!empty($filters['platform'])) {
                $query->byPlatform($filters['platform']);
            }
            
            $tasks = $query->orderBy('created_at', 'desc')
                ->paginate($perPage, ['*'], 'page', $page);

            $tasksData = $tasks->map(function ($task) {
                return [
                    'id' => $task->id,
                    'task_type' => $task->task_type,
                    'status' => $task->status,
                    'platform' => $task->platform,
                    'model_name' => $task->model_name,
                    'cost' => $task->cost,
                    'tokens_used' => $task->tokens_used,
                    'processing_time_ms' => $task->processing_time_ms,
                    'created_at' => $task->created_at->format('Y-m-d H:i:s'),
                    'completed_at' => $task->completed_at?->format('Y-m-d H:i:s')
                ];
            });

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => 'success',
                'data' => [
                    'tasks' => $tasksData,
                    'pagination' => [
                        'current_page' => $tasks->currentPage(),
                        'total' => $tasks->total(),
                        'per_page' => $tasks->perPage(),
                        'last_page' => $tasks->lastPage()
                    ]
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'user_id' => $userId,
                'filters' => $filters,
                'page' => $page,
                'per_page' => $perPage,
            ];

            Log::error('获取用户任务列表失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '获取任务列表失败',
                'data' => null
            ];
        }
    }

    /**
     * 重试任务
     */
    public function retryTask(int $taskId, int $userId): array
    {
        try {
            $task = AiGenerationTask::byUser($userId)->find($taskId);
            
            if (!$task) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '任务不存在',
                    'data' => []
                ];
            }

            if (!$task->canRetry()) {
                return [
                    'code' => ApiCodeEnum::INVALID_OPERATION,
                    'message' => '任务无法重试',
                    'data' => []
                ];
            }

            $task->incrementRetry();

            // 根据任务类型重新执行任务
            $taskType = $task->task_type;

            // 判断是否为文本生成类型的任务
            $textGenerationTypes = [
                AiGenerationTask::TYPE_TEXT_GENERATION,
                AiGenerationTask::TYPE_TEXT_GENERATION_PROMPT,
                AiGenerationTask::TYPE_TEXT_GENERATION_STORY,
                AiGenerationTask::TYPE_TEXT_GENERATION_STORYBOARD,
                AiGenerationTask::TYPE_CHARACTER_GENERATION
            ];

            if (in_array($taskType, $textGenerationTypes)) {
                $this->executeTextGeneration($task);
            } else {
                // 其他类型的任务暂不支持重试
                throw new \Exception("暂不支持重试任务类型: {$taskType}");
            }

            Log::info('任务重试成功', [
                'task_id' => $taskId,
                'user_id' => $userId,
                'retry_count' => $task->retry_count
            ]);

            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '任务重试成功',
                'data' => [
                    'task_id' => $task->id,
                    'status' => $task->status,
                    'retry_count' => $task->retry_count
                ]
            ];

        } catch (\Exception $e) {
            $error_context = [
                'task_id' => $taskId,
                'user_id' => $userId,
            ];

            Log::error('任务重试失败', [
                'method' => __METHOD__,
                'error_context' => LogCheckHelper::sanitize_request_for_log($error_context),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '任务重试失败',
                'data' => null
            ];
        }
    }

    /**
     * 执行文本生成（调用AI服务）
     *
     * 🚨 架构边界规范：
     * ✅ 工具API接口服务负责调用AI服务，不进行模拟
     * ✅ 使用 AiServiceClient 实现环境切换
     * ❌ 不在工具API中进行模拟返回数据
     *
     * 🚨 升级：支持用户平台选择和偏好记录
     */
    private function executeTextGeneration(AiGenerationTask $task): array
    {
        try {
            $task->start();

            $requestData = [
                'model' => $task->model_name,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $task->getInputData('prompt')
                    ]
                ],
                'max_tokens' => $task->getInputData('max_tokens', 1000),
                'temperature' => $task->getInputData('temperature', 0.7),
                'top_p' => $task->getInputData('top_p', 0.9)
            ];

            // 🚨 升级：使用 callWithUserChoice 方法，支持用户偏好记录
            $response = \App\Services\AiServiceClient::callWithUserChoice(
                $task->platform,
                $task->task_type,
                $requestData,
                $task->user_id
            );

            if ($response['success']) {
                $aiResponse = $response['data'];

                // 解析AI服务响应
                $generatedText = $aiResponse['choices'][0]['message']['content'] ?? '';
                $tokensUsed = $aiResponse['usage']['total_tokens'] ?? 0;

                $outputData = [
                    'text' => $generatedText,
                    'finish_reason' => $aiResponse['choices'][0]['finish_reason'] ?? 'stop',
                    'model' => $aiResponse['model'] ?? $task->model_name,
                    'usage' => $aiResponse['usage'] ?? []
                ];

                $task->complete($outputData, $tokensUsed);

                // 消费冻结积分
                $this->pointsService->consumePoints($task->id);

                // 返回数据
                $responseData = [
                    'task_id' => $task->id,                                    // 任务ID
                    'status' => 'completed',                                   // 任务状态：已完成
                    'result' => [                                              // 生成结果
                        'text' => $generatedText,                             // AI生成的文本内容
                        'finish_reason' => $outputData['finish_reason'],      // 完成原因（stop/length/content_filter等）
                        'model' => $outputData['model'],                      // 使用的AI模型名称
                        'usage' => $outputData['usage'],                      // Token使用统计信息
                        'tokens_used' => $tokensUsed                          // 总Token消耗数量
                    ],
                    'project_id' => $task->project_id,                        // 关联的项目ID（可为null）
                    'context' => $task->context,                              // 生成上下文（prompt_edit等）
                    'processing_time_ms' => $task->processing_time_ms,        // 处理耗时（毫秒）
                    'timestamp' => Carbon::now()->format('c')                 // 完成时间戳（ISO 8601格式）
                ];

                return [
                    'code' => ApiCodeEnum::SUCCESS,
                    'message' => '文本生成成功',
                    'data' => $responseData
                ];
            } else {
                throw new \Exception($response['error'] ?? 'AI服务调用失败');
            }

        } catch (\Exception $e) {
            $task->fail($e->getMessage());

            // 释放积分
            $this->pointsService->releasePoints($task->id);

            Log::error('文本生成任务失败', [
                'task_id' => $task->id,
                'platform' => $task->platform,
                'error' => $e->getMessage()
            ]);

            // 返回失败数据
            return [
                'code' => ApiCodeEnum::MY_SERVICE_ERROR,
                'message' => '文本生成失败',
                'data' => [
                    'task_id' => $task->id,
                    'status' => 'failed',
                    'error' => $e->getMessage(),
                    'timestamp' => Carbon::now()->format('c')
                ]
            ];
        }
    }





    /**
     * WebSocket文本生成积分预检查
     * 在创建异步任务之前检查积分是否充足
     *
     * @param int $userId 用户ID
     * @param string $prompt 提示词
     * @param int|null $modelId 模型ID
     * @param array $generationParams 生成参数
     * @return array 检查结果
     */
    public function checkPointsForWebSocketTextGeneration(int $userId, string $prompt, ?int $modelId = null, array $generationParams = []): array
    {
        try {
            // 解析prompt获取实际提示词
            $promptData = json_decode($prompt, true);
            $actualPrompt = $promptData['提示词'] ?? $prompt;

            // 获取模型配置
            if ($modelId) {
                $model = AiModelConfig::active()->find($modelId);
            } else {
                $model = $this->aiModelService->getUserDefaultModel($userId, AiModelConfig::TYPE_TEXT_GENERATION);
            }

            if (!$model) {
                return [
                    'code' => ApiCodeEnum::NOT_FOUND,
                    'message' => '没有可用的文本生成模型',
                    'data' => []
                ];
            }

            // 计算预估成本
            $estimatedCost = $this->calculateTextGenerationCost($model, $actualPrompt, $generationParams);

            // 检查积分是否充足
            $pointsCheck = $this->pointsService->checkPoints($userId, $estimatedCost, 'text_generation');

            if ($pointsCheck['code'] !== ApiCodeEnum::SUCCESS || !$pointsCheck['data']['sufficient']) {
                return [
                    'code' => ApiCodeEnum::INSUFFICIENT_POINTS,
                    'message' => '积分不足',
                    'data' => $pointsCheck['data'] ?? []
                ];
            }

            // 积分检查通过
            return [
                'code' => ApiCodeEnum::SUCCESS,
                'message' => '积分检查通过',
                'data' => [
                    'estimated_cost' => $estimatedCost,
                    'model' => $model,
                    'available_points' => $pointsCheck['data']['current_balance']
                ]
            ];

        } catch (\Exception $e) {
            Log::error('WebSocket文本生成积分预检查失败', [
                'user_id' => $userId,
                'model_id' => $modelId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'code' => ApiCodeEnum::INTERNAL_ERROR,
                'message' => '积分检查失败：' . $e->getMessage(),
                'data' => []
            ];
        }
    }

    /**
     * 计算文本生成成本
     */
    private function calculateTextGenerationCost(AiModelConfig $model, string $prompt, array $params): float
    {
        $inputTokens = strlen($prompt) / 4; // 简单估算
        $maxTokens = $params['max_tokens'] ?? 1000;
        $totalTokens = $inputTokens + $maxTokens;

        return round($totalTokens * $model->cost_per_request, 4);
    }
}
